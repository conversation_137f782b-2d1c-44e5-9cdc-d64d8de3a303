
import { PortfolioItem } from '../types';

export const desktopIcons = [
  { id: 'my_computer', name: 'My Computer', icon: '💻', type: 'system' },
  { id: 'projects', name: 'My Projects', icon: '📁', type: 'folder' },
  { id: 'my_resume', name: 'My Resume.pdf', icon: '📄', type: 'file' },
  { id: 'skills', name: 'Skills & Experience', icon: '🧰', type: 'folder' },
  { id: 'certifications', name: 'Certifications', icon: '✉️', type: 'folder' },
  // { id: 'outlook_mail', name: 'Outlook Mail', icon: '📧', type: 'shortcut' },
  { id: 'contact', name: 'Contact Info', icon: '💌', type: 'shortcut' },
  { id: 'about', name: 'About Me', icon: '🙈', type: 'file' },
  { id: 'recyclebin', name: 'Recycle Bin', icon: '🗑️', type: 'system' },
];

export const portfolioItems: PortfolioItem[] = [
  { id: 'my_computer', name: 'My Computer', icon: '💻' },
  { id: 'projects', name: 'My Projects', icon: '📁' },
  { id: 'my_resume', name: 'My Resume.pdf', icon: '📄' },
  { id: 'skills', name: 'Skills & Experience', icon: '🧰' },
  { id: 'certifications', name: 'Certifications', icon: '✉️' },
  { id: 'contact', name: 'Contact Info', icon: '💌' },
  { id: 'about', name: 'About Me', icon: '🙈' },
  // { id: 'outlook_mail', name: 'Outlook Mail', icon: '📧' },
];
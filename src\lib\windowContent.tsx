import React from 'react';
import RecycleBinContent from '../components/windows/RecycleBinContent';
import SkillsContent from '../components/windows/SkillsContent';
import MyComputerContent from '../components/windows/MyComputerContent';
import MyDocumentsContent from '../components/windows/MyDocumentsContent';
import MyPicturesContent from '../components/windows/MyPicturesContent';
import MyMusicContent from '../components/windows/MyMusicContent';
import ControlPanelContent from '../components/windows/ControlPanelContent';
import ResumeContent from '../components/windows/ResumeContent';
import ContactContent from '../components/windows/ContactContent';
import ProjectFolder from '../components/ProjectFolder';
import ProjectViewer from '../components/windows/ProjectViewer';
import OutlookMailContent from '../components/windows/OutlookMailContent';

import WindowsFolderHeader from '../components/windows/WindowsFolderHeader';
import { Award, BarChart3, FolderOpen } from 'lucide-react';
import { getBrowserExtensionFolders } from '@shared/lib/browserExtensions';

type OpenWindowFunc = (id: string, name: string) => void;
type UpdateWindowSizeFunc = (windowId: string, size: { width: number; height: number }) => void;

interface WindowContentOptions {
  onOpenWindow: OpenWindowFunc;
  updateWindowSize?: UpdateWindowSizeFunc;
  windowId?: string;
}

export const getWindowContent = (iconId: string, options: OpenWindowFunc | WindowContentOptions): React.ReactNode => {
  // Handle backward compatibility - if options is a function, it's the old onOpenWindow parameter
  const onOpenWindow = typeof options === 'function' ? options : options.onOpenWindow;
  const updateWindowSize = typeof options === 'object' ? options.updateWindowSize : undefined;
  const windowId = typeof options === 'object' ? options.windowId : undefined;
  switch (iconId) {
    case 'recyclebin':
      return <RecycleBinContent onOpenWindow={onOpenWindow} />;
    case 'my_computer':
      return <MyComputerContent onOpenWindow={onOpenWindow} />;
    case 'skills':
      return <SkillsContent />;
    case 'my_documents':
      return <MyDocumentsContent onOpenWindow={onOpenWindow} />;
    case 'my_pictures':
      return <MyPicturesContent onOpenWindow={onOpenWindow} />;
    case 'my_music':
      return <MyMusicContent />;
    case 'control_panel':
      return <ControlPanelContent onOpenWindow={onOpenWindow} />;
    case 'certifications':
      return (
        <div className="bg-white font-tahoma">
          <WindowsFolderHeader
            title="Professional Certifications"
            subtitle="Continuous learning achievements"
            icon={<Award size={22} className="text-white" />}
            variant="system"
          />
          <div className="p-4">

          <div className="space-y-4">
            {/* Udemy Certificate */}
            <div className="bg-gray-50 p-4 rounded border hover:bg-blue-50 transition-colors">
              <div className="flex items-start space-x-3">
                <div className="w-12 h-12 bg-purple-500 rounded flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-xl">📚</span>
                </div>
                <div className="flex-grow">
                  <h3 className="font-bold text-blue-900 mb-1">Udemy Certificate</h3>
                  <p className="text-sm text-gray-600 mb-2">SQL for Data Analysis: Advanced SQL Querying Techniques</p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-gray-700">
                    <div><strong>Issue Date:</strong> January 2025</div>
                    <div><strong>Credential ID:</strong> UC-4119aa4c-b6d3-452a-9a61-5f8c8b65f1b7</div>
                  </div>
                  <a
                    href="https://www.udemy.com/certificate/UC-4119aa4c-b6d3-452a-9a61-5f8c8b65f1b7/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-block mt-2 text-sm text-blue-600 hover:underline"
                  >
                    View Certificate →
                  </a>
                </div>
              </div>
            </div>

            {/* CodeSignal Certificate */}
            <div className="bg-gray-50 p-4 rounded border hover:bg-blue-50 transition-colors">
              <div className="flex items-start space-x-3">
                <div className="w-12 h-12 bg-green-500 rounded flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-xl">💻</span>
                </div>
                <div className="flex-grow">
                  <h3 className="font-bold text-blue-900 mb-1">CodeSignal Certificate</h3>
                  <p className="text-sm text-gray-600 mb-2">Mastering Algorithms and Data Structures in C#</p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-gray-700">
                    <div><strong>Issue Date:</strong> January 2025</div>
                    <div><strong>Platform:</strong> CodeSignal</div>
                  </div>
                  <a
                    href="https://codesignal.com/learn/certificates/cm4954vlx007fds9n0x4tg6o2/course-paths/108"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-block mt-2 text-sm text-blue-600 hover:underline"
                  >
                    View Certificate →
                  </a>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-50 rounded border-l-4 border-blue-500">
            <p className="text-sm text-blue-800">
              <strong>Continuous Learning:</strong> I actively pursue professional development through
              online courses, side projects, and certifications to stay current with industry trends and best practices.
            </p>
          </div>
          </div>
        </div>
      );
    case 'projects':
      const browserExtensionFolders = getBrowserExtensionFolders();
      return (
        <div className="bg-white font-tahoma h-full">
          <WindowsFolderHeader
            title="My Projects"
            icon={<FolderOpen size={16} className="text-blue-600" />}
            variant="default"
            onBackClick={() => onOpenWindow('my_documents', 'My Documents')}
            onFoldersClick={() => console.log('Toggle folders')}
            onOpenWindow={onOpenWindow}
          />
          <div className="p-4 sm:p-6 bg-gradient-to-br from-gray-50 to-blue-50 min-h-full overflow-auto">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 max-w-5xl mx-auto justify-items-center">
              {browserExtensionFolders.map((folder) => (
                <ProjectFolder
                  key={folder.projectId}
                  name={folder.name}
                  description={folder.description}
                  icon={folder.icon}
                  onClick={() => onOpenWindow(`project-${folder.projectId}`, folder.windowName)}
                />
              ))}
              <ProjectFolder
                name="Mobile App"
                description="Rental Mobile App"
                icon="🚚"
                onClick={() => onOpenWindow('project-moverzz', 'a work in progress web and mobile app (android and ios) - for real estate')}
              />
              <ProjectFolder
                name="NPM Command Runner"
                description="VS Code Extension"
                icon="⚡"
                onClick={() => onOpenWindow('project-npm-command-runner', 'NPM Command Runner - VS Code Extension')}
              />
              {/* <ProjectFolder
                name="E-Commerce Platform"
                description="Full-stack Web App"
                icon="🛍️"
                onClick={() => onOpenWindow('project-ecommerce', 'E-Commerce Platform')}
              /> */}
            </div>
          </div>
        </div>
      );
    case 'my_resume':
      return <ResumeContent updateWindowSize={updateWindowSize} windowId={windowId} />;
    case 'contact':
      return <ContactContent />;
    case 'about':
      return (
        <div className="p-4 bg-white font-mono text-sm">
          <div className="whitespace-pre-line">
            <div>
              Hello! I'm Mark Jovet Verano (markoverano), a passionate software engineer with over a decade of experience building applications and scalable systems.
              {'\n\n'}
              I specialize in creating robust, user-focused software solutions that bridge the gap between complex technical requirements and intuitive user experiences. My journey in software engineering has been driven by a commitment to excellence and continuous learning.
              {'\n\n'}
              What I Do:
              {'\n'}
              - Full-stack development with .NET, Node.js, Angular and React
              {'\n'}
              - Cloud architecture and DevOps implementation
              {'\n'}
              - Mobile app development for iOS and Android
              {'\n'}
              - System design and performance optimization
              {'\n'}
              - Database design and management
              {'\n\n'}
              My Professional Approach:
              {'\n'}
              I believe in writing clean, maintainable code and creating solutions that truly serve users' needs. I'm always excited to learn new technologies and tackle challenging problems that push the boundaries of what's possible.
              {'\n\n'}
              Current Technical Focus:
              {'\n'}
              - Microservices architecture and distributed systems
              {'\n'}
              - AI/ML integration in applications
              {'\n'}
              - Performance optimization and scalability
              {'\n'}
              - Team leadership and technical mentoring
              {'\n'}
              - Modern web technologies and frameworks
              {'\n'}
              - Mobile app development
              {'\n\n'}
              When I'm not coding, you can find me:
              {'\n'}
              - Working on innovative side projects (wait, that's still coding!)
              {'\n'}
              - Reading about emerging technologies and industry trends
              {'\n'}
              - Hiking, swimming, camping, and enduro riding
              {'\n\n'}
              Thanks for visiting my fully responsive and mobile-friendly Windows XP themed portfolio. This nostalgic interface showcases my technical skills while paying homage to the golden age of computing.
              {'\n\n'}
              Feel free to explore{' '}
              <button
                onClick={() => onOpenWindow('projects', 'My Projects')}
                className="text-blue-600 hover:text-blue-800 underline cursor-pointer bg-transparent border-none p-0 font-mono text-sm"
              >
                my side projects
              </button>
              {' '}and{' '}
              <button
                onClick={() => onOpenWindow('contact', 'Contact Info')}
                className="text-blue-600 hover:text-blue-800 underline cursor-pointer bg-transparent border-none p-0 font-mono text-sm"
              >
                get in touch
              </button>
              {' '}to discuss potential partnerships!
              {'\n\n'}
              ---
              {'\n'}
              Portfolio by &copy;Mark Jovet Verano (markoverano) 2025. All rights reserved.
              {'\n'}
              Last modified: June 2025
              {'\n'}
              File size: 1.8 KB
            </div>
          </div>
        </div>
      );
    case 'project-zendo':
      return <ProjectViewer projectId="zendo" onNavigateBack={() => onOpenWindow('projects', 'My Projects')} />;
    case 'project-chromepanion':
      return <ProjectViewer projectId="chromepanion" onNavigateBack={() => onOpenWindow('projects', 'My Projects')} />;
    case 'project-moverzz':
      return <ProjectViewer projectId="moverzz" onNavigateBack={() => onOpenWindow('projects', 'My Projects')} />;
    case 'project-npm-command-runner':
      return <ProjectViewer projectId="npm-command-runner" onNavigateBack={() => onOpenWindow('projects', 'My Projects')} />;
    case 'project-ecommerce':
      return <ProjectViewer projectId="ecommerce" onNavigateBack={() => onOpenWindow('projects', 'My Projects')} />;
    case 'portfolio_analytics':
      return (
        <div className="bg-white font-tahoma">
          <WindowsFolderHeader
            title="Portfolio Analytics"
            subtitle="Performance metrics and insights"
            icon={<BarChart3 size={22} className="text-white" />}
            variant="system"
          />
          <div className="p-4">

          <div className="space-y-4">
            <div className="bg-blue-50 p-4 rounded border-l-4 border-blue-500">
              <h3 className="font-bold text-blue-900 mb-2">Portfolio Performance</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Total Visits:</span>
                  <span className="font-semibold ml-2">Coming Soon</span>
                </div>
                <div>
                  <span className="text-gray-600">Unique Visitors:</span>
                  <span className="font-semibold ml-2">Coming Soon</span>
                </div>
                <div>
                  <span className="text-gray-600">Page Views:</span>
                  <span className="font-semibold ml-2">Coming Soon</span>
                </div>
                <div>
                  <span className="text-gray-600">Avg. Session:</span>
                  <span className="font-semibold ml-2">Coming Soon</span>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 p-4 rounded border border-yellow-200">
              <p className="text-sm text-yellow-800">
                <strong>Analytics Integration:</strong> Portfolio analytics will be implemented with privacy-focused
                tracking to provide insights into visitor engagement while respecting user privacy.
              </p>
            </div>
          </div>
          </div>
        </div>
      );
    case 'outlook_mail':
      return <OutlookMailContent />;

    default:
      return <div className="p-4">Window content not found.</div>;
  }
};
